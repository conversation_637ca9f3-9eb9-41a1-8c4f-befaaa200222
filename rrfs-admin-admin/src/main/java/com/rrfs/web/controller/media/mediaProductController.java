package com.rrfs.web.controller.media;


import com.rrfs.common.core.domain.R;
import com.rrfs.system.pojo.po.MediaProductInfoPO;
import com.rrfs.system.service.table.IMediaProductInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Slf4j
@Controller
@RequestMapping("mediaProduct")
public class mediaProductController {
@Autowired
private IMediaProductInfoService mediaProductInfoService;


    @PostMapping("addMediaProduct")
    @ResponseBody
    public R<String> addMediaProduct(@RequestBody MediaProductInfoPO po) {
        if (po.getMedia().equals("百度")){
            po.setAccountGroupId(null);
        }
        mediaProductInfoService.save(po);
        return R.ok();
    }
}
