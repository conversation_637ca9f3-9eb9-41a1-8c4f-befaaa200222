package com.rrfs.system.pojo.po.baidu;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("baidu_performance_detail")
public class BaiduPerformanceDetailPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 日期
     */
    private LocalDate dateFlag;

    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 框架号
     */
    private String framePolicyNo;

    /**
     * 合同广告主
     */
    private String custName;

    /**
     * 消费广告主
     */
    private String customerCrmName;

    /**
     * 代理公司
     */
    private String agentName;

    /**
     * 代理集团
     */
    private String agentGroupName;

    /**
     * 代理商ALB code
     */
    private String agentAlbCode;

    /**
     * 产品线
     */
    private String prodlineName;

    /**
     * 产品线大类
     */
    private String monitorProductName;

    /**
     * 产品线小类
     */
    @TableField(value = "prodline_name_2nd")
    private String prodlineName2nd;

    /**
     * 业绩
     */
    private BigDecimal performance;

    /**
     * 业绩类别
     */
    private String consumptionType;

    /**
     * 客户经理
     */
    private String salesMgrName;

    /**
     * MEG客户一级行业
     */
    @TableField(value = "ssg_trade_name_1_new")
    private String ssgTradeName1New;

    /**
     * 渠道经理
     */
    private String channleName;

    /**
     * MEG客户二级行业
     */
    @TableField(value = "ssg_trade_name_2_new")
    private String ssgTradeName2New;

    /**
     * 消费广告主ALB code
     */
    private String customerCrmAlbCode;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}