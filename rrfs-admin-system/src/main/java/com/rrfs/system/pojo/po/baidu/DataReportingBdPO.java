package com.rrfs.system.pojo.po.baidu;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value  ="bd_data_reporting")
public class DataReportingBdPO {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField(value  ="report_type")
    private String reportType;
    @TableField(value  ="date")
    private String date;
    @TableField(value  ="user_name")
    private String userName;
    @TableField(value  ="impression")
    private Integer impression;
    private Integer click;

    private  BigDecimal cost;

    private  BigDecimal cpc;

    private  double ctr;
    @TableField(value  ="user_ids")
    private String userIds;

    @TableField(value  ="user_id")
    private String userId;

    @TableField(value  ="region")
    private String region;

    private String butlerName;


}
