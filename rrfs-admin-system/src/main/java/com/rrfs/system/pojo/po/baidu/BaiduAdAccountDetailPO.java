package com.rrfs.system.pojo.po.baidu;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 百度广告账号明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("baidu_ad_account_detail")
public class BaiduAdAccountDetailPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据日期
     */
    private LocalDate date;

    /**
     * 授权账号id
     */
    private Long authAccountId;

    /**
     * 授权账号名称
     */
    private String authAccountName;

    /**
     * 账号id
     */
    private Long adAccountId;

    /**
     * 账号名称
     */
    private String adAccountName;

    /**
     * 消耗
     */
    private BigDecimal cost;

}
